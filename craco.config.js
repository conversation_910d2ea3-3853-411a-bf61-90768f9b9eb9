const { ModuleFederationPlugin } = require('webpack').container;

module.exports = {
  webpack: {
    configure: (config) => {
      config.plugins.push(
        new ModuleFederationPlugin({
          name: 'host',
          remotes: {
            incidentApp: 'incidentApp@http://localhost:8080/assets/remoteEntry.js',
          },
          shared: {
            react: { singleton: true, requiredVersion: '^18.2.0' },
            'react-dom': { singleton: true, requiredVersion: '^18.2.0' },
            'react-router-dom': { singleton: true, requiredVersion: '^6.3.0' },
          },
        })
      );
      return config;
    },
  },
};
