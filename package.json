{"name": "staradmin-react-pro", "proxy": "https://sttgdcesmdev.service-now.com", "version": "1.0.0", "private": true, "dependencies": {"@aldabil/react-scheduler": "^2.7.26", "@date-io/moment": "^2.17.0", "@delowar/react-circle-progressbar": "^1.0.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@formkit/auto-animate": "^1.0.0-beta.6", "@fullcalendar/core": "^4.3.1", "@fullcalendar/daygrid": "^4.3.0", "@fullcalendar/interaction": "^4.3.0", "@fullcalendar/react": "^4.3.0", "@fullcalendar/timegrid": "^4.3.0", "@lourenci/react-kanban": "^2.1.0", "@material-ui/core": "^4.12.4", "@material-ui/icons": "^4.11.3", "@mdi/font": "^7.2.96", "@mdi/js": "^7.3.67", "@mdi/react": "^1.6.1", "@mui/icons-material": "^5.13.7", "@mui/lab": "^5.0.0-alpha.135", "@mui/material": "^5.13.7", "@mui/styles": "^5.13.7", "@mui/x-date-pickers": "^6.18.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@react-pdf/renderer": "^3.3.1", "@reduxjs/toolkit": "^1.9.3", "@stitches/react": "^1.2.8", "@tinymce/tinymce-react": "^4.1.0", "@viniarruda/react-month-range-picker": "^2.0.4", "amazon-cognito-identity-js": "^6.2.0", "apexcharts": "^3.43.0", "aws-amplify": "^5.0.25", "axios": "^1.3.5", "bootstrap": "^5.2.0", "bosket": "^0.2.3", "brace": "^0.11.1", "bs-custom-file-input": "^1.3.4", "canvasjs": "^1.8.3", "chart.js": "^2.9.4", "chartist": "^0.11.4", "cogo-toast": "^4.2.3", "compass-mixins": "^0.12.12", "datatables.net": "^1.13.4", "datatables.net-bs4": "^1.13.4", "date-fns": "^2.30.0", "easymde": "^2.16.1", "emotion": "^11.0.0", "feather-icons-react": "^0.7.0", "flag-icon-css": "^3.3.0", "font-awesome": "^4.7.0", "formBuilder": "^3.17.0", "google-maps-react": "^2.0.6", "highcharts": "^12.1.2", "highcharts-export-data": "^0.1.7", "highcharts-exporting": "^0.1.7", "highcharts-react-official": "^3.2.1", "html-to-pdfmake": "^2.5.1", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "material-table": "^2.0.5", "material-ui-dropzone": "^3.5.0", "moment-timezone": "^0.5.45", "namor": "^2.0.4", "nouislider-react": "^3.4.1", "path-browserify": "^1.0.1", "pdfjs-dist": "^4.0.379", "pdfmake": "^0.2.9", "primeicons": "^6.0.1", "primereact": "^10.5.1", "puse-icons-feather": "^1.1.0", "qs": "^6.12.0", "react": "^18.2.0", "react-ace": "^10.1.0", "react-apexcharts": "^1.4.1", "react-beautiful-dnd": "^13.1.0", "react-bootstrap": "^2.4.0", "react-bootstrap-editable": "^0.6.1", "react-bootstrap-table-next": "^3.2.0", "react-bootstrap-table2-paginator": "^2.1.2", "react-bootstrap-table2-toolkit": "^2.1.0", "react-bootstrap-typeahead": "^3.4.7", "react-c3js": "^0.1.20", "react-chartist": "^0.14.4", "react-chartjs-2": "^2.11.2", "react-circular-progressbar": "^2.1.0", "react-clipboard.js": "^2.0.16", "react-color": "^2.19.3", "react-contextmenu": "^2.14.0", "react-data-sort": "^1.2.1", "react-datepicker": "^2.16.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dnd-multi-backend": "^6.0.2", "react-dnd-touch-backend": "^16.0.1", "react-dom": "^18.2.0", "react-dragula": "^1.1.17", "react-dropzone": "^10.1.9", "react-gauge-chart": "^0.4.0", "react-google-charts": "^4.0.0", "react-grid-gallery": "^0.5.5", "react-images": "^1.1.7", "react-input-mask": "^3.0.0-alpha.0", "react-is": "^18.2.0", "react-jvectormap": "0.0.16", "react-multistep": "5.3.0", "react-pdf": "^7.7.0", "react-photo-gallery": "^8.0.0", "react-qr-code": "^2.0.14", "react-quill": "^1.3.5", "react-rating": "^1.7.2", "react-redux": "^8.0.5", "react-router": "^6.3.0", "react-router-dom": "^5.0.1", "react-scripts": "5.0.1", "react-select": "^3.0.8", "react-signature-canvas": "^1.0.6", "react-simple-maps": "^3.0.0", "react-simplemde-editor": "^5.0.2", "react-slick": "^0.29.0", "react-sparklines": "^1.7.0", "react-sticky": "^6.0.3", "react-svg-gauge": "^1.0.10", "react-switch": "^7.0.0", "react-table": "^6.10.3", "react-tag-autocomplete": "^5.11.1", "react-to-print": "^3.0.6", "react-treebeard": "^3.2.4", "react-trello": "^2.2.11", "reactjs-human-body": "^0.0.3", "recharts": "^2.9.3", "sass": "^1.54.0", "simple-line-icons": "^2.5.5", "slick-carousel": "^1.8.1", "stream-browserify": "^3.0.0", "sweetalert2": "^11.7.3", "sweetalert2-react": "^0.8.3", "ti-icons": "^0.1.2", "typicons.font": "^2.1.2", "use-force-update": "^1.0.11", "uuid": "^9.0.1", "xlsx": "^0.18.5"}, "scripts": {"start": "set PORT=3001 && craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@craco/craco": "^7.1.0", "@svgr/cli": "^6.3.1", "@types/react-test-renderer": "^18.0.0", "babel-loader": "^8.4.1", "css-loader": "^7.1.2", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.3", "react-test-renderer": "^18.2.0", "sass-loader": "^16.0.5", "style-loader": "^4.0.0", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}}