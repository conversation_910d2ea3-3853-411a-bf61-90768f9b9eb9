import React, { useEffect, useState, useMemo, useRef } from 'react'

import { Modal, Button, Form, Dropdown } from 'react-bootstrap';


import moment from 'moment-timezone';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import Box from '@mui/material/Box';
import Select from 'react-select'
import FormRender from '../../apps/FormRender';
import ActionTable from './ActionTable';
import { MultiSelect } from 'primereact/multiselect';
import { FilterMatchMode, FilterOperator, FilterService } from 'primereact/api';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { useReactToPrint } from 'react-to-print';
import * as XLSX from 'xlsx';
import { API_URL } from '../../constants';


const InspectionTable = ({ inspection }) => {



    const contentRef = useRef()


    const [actionModal, setActionModal] = useState(false)
    const [current, setCurrent] = useState('')
    const [auditActionModal, setAuditActionModal] = useState(false)
    const [maskId, setMaskId] = useState('')
    const [totalAction, setTotalAction] = useState('')

    const [filters, setFilters] = useState({
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        maskId: { value: null, matchMode: FilterMatchMode.IN },
        "checklist.name": { value: null, matchMode: FilterMatchMode.IN },
        "assignedTo.firstName": { value: null, matchMode: FilterMatchMode.IN },
        status: { value: null, matchMode: FilterMatchMode.IN },
        permitType: { value: null, matchMode: FilterMatchMode.IN },
    });


    const [data, setData] = useState(null);
    const [filterData, setFilterData] = useState([]);
    const [checklist, setChecklist] = useState([])
    const [assignee, setAssignee] = useState([])

    useEffect(() => {
        if (inspection) {
            setFilterData(inspection)

            const obs = inspection.map(item => {
                return { name: item.checklist?.name || '', value: item.checklist?.name || '' }
            })
            setChecklist(obs.filter((ele, ind) => ind === obs.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))

            const obs1 = inspection.map(item => {
                return { name: item.assignedTo?.firstName || '', value: item.assignedTo?.firstName || '' }
            })
            setAssignee(obs1.filter((ele, ind) => ind === obs1.findIndex(elem => elem.value === ele.value && elem.name === ele.name)))
        }
    }, [inspection])

    // const getInspectionData = async () => {
    //     const params = {
    //         "include": [{ "relation": "assignedTo" }, { "relation": "locationOne" }, { "relation": "locationTwo" }, { "relation": "locationThree" }, { "relation": "locationFour" }, { "relation": "checklist" }]

    //     };
    //     const response = await API.get(`${INSPECTION_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);


    //     if (response.status === 200) {
    //         setData(response.data)
    //         setFilterData(response.data)
    //     }
    // }


    const [showModal, setShowModal] = useState(false)





    const viewInspection = (row) => {


        // Set the current row for further operations (assuming `setCurrent` is available in your scope)
        setCurrent(row);
        console.log(row, ' Inspection Row')
        // Group actions by their description using the `groupByDescription` function
        const totalActionData = groupByDescription(row.inspectionData.totalActions);


        // Filter the grouped actions to find those that are 'approve' and 'submitted'
        // const totalCompleted = totalActionData.filter(
        //     (item) => item.lastActionType === 'approve' && item.lastStatus === 'submitted'
        // // );
        // console.log('Total completed actions:', totalCompleted);

        // Example: Extracting maskId and action counts if needed
        const id = row.maskId;
        // Assuming you want to store the count of completed actions

        // Update the state with the maskId and the number of completed actions
        setMaskId(id);
        setTotalAction(totalActionData);

        setData(row)
        setShowModal(true)
    }
    const viewTemplate = (option) => {
        return (
            <i onClick={() => viewInspection(option)} style={{ fontSize: '18px' }} className='mdi mdi-eye'></i>
        )

    }

    const maskIdBodyTemplate = (row) => {


        // Render the clickable mask ID with a callback to view more details about the inspection
        return (
            <div className='maskid' onClick={() => viewInspection(row)}>
                {row.maskId}
            </div>
        );
    };


    const locationBodyTemplate = (rowData) => {


        return ` ${rowData.locationFour?.name || ''}`



    }
    function groupByDescription(data) {
        console.log(data);
        console.log('in');

        const filterData = data.filter(item => item.actionType !== 'inspect')

        const groupedData = [];
        const descriptionMap = {};

        filterData.forEach(item => {
            const { objectId, description, actionType, assignedToId, status } = item;
            if (!descriptionMap[description]) {
                descriptionMap[description] = {
                    objectId: objectId,
                    firstActionType: actionType,
                    lastActionType: actionType,
                    actionTypes: [actionType],
                    lastAssignedToId: assignedToId,
                    lastStatus: status,
                    data: []
                };
            } else {
                descriptionMap[description].lastActionType = actionType;
                descriptionMap[description].actionTypes.push(actionType);
                descriptionMap[description].lastAssignedToId = assignedToId;
                descriptionMap[description].lastStatus = status;

            }
            descriptionMap[description].data.push(item);
        });

        // Update lastActionType, lastAssignedToId, and lastStatus in each group
        for (const description in descriptionMap) {
            const group = descriptionMap[description];
            const lastDataObject = group.data[group.data.length - 1];
            group.lastActionType = lastDataObject.actionType;
            group.lastAssignedToId = lastDataObject.assignedToId;
            group.lastStatus = lastDataObject.status;
            groupedData.push(group);
        }

        return groupedData;
    }


    const actionBodyTemplate = (rowData) => {


        const totalActionData = groupByDescription(rowData.inspectionData.totalActions)



        const totalCompleted = totalActionData.filter(item => item.lastActionType === 'approve' && item.lastStatus === 'submitted')

        const color = totalActionData.length === totalCompleted.length ? 'greenBox' : totalCompleted.length === 0 ? 'redBox' : 'orangeBox';




        // Return the link with dynamic styles and counts
        return <a href="#" onClick={(e) => { e.preventDefault(); handleAuditActionCard(rowData, totalActionData) }} className={color} > {totalCompleted.length} / {totalActionData.length}</a>;
    }

    // Function to determine status based on action completion ratio
    const getStatusFromActionRatio = (rowData) => {
        const totalActionData = groupByDescription(rowData.inspectionData.totalActions);
        const totalCompleted = totalActionData.filter(item => item.lastActionType === 'approve' && item.lastStatus === 'submitted');

        // If ratio is 3/3 (all actions completed)
        if (totalActionData.length > 0 && totalActionData.length === totalCompleted.length) {
            return "Completed with all actions closed";
        }
        // If ratio is 2/3 (some actions completed)
        else if (totalActionData.length > 0 && totalCompleted.length < totalActionData.length) {
            return "Completed with Open Actions";
        }
        // If ratio is 0/0 (no actions)
        else if (totalActionData.length === 0) {
            return "Completed";
        }

        // Return the original status if none of the conditions match
        return rowData.status;
    }

    const handleAuditActionCard = (data, actions) => {


        setAuditActionModal(true)
        setCurrent(data)

        setMaskId(data.maskId)
        setTotalAction(actions)
    }
    const representativesItemTemplate = (option) => {
        return (
            <div className="flex align-items-center gap-2">

                <span>{option.value}</span>
            </div>
        );
    }
    const checklistFilterTemplate = (options) => {

        return (
            <React.Fragment>

                <MultiSelect value={options.value} options={checklist} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }

    const assigneeFilterTemplate = (options) => {

        return (
            <React.Fragment>

                <MultiSelect value={options.value} options={assignee} itemTemplate={representativesItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" placeholder="Any" className="p-column-filter" />
            </React.Fragment>
        );
    }
    const generatePdf = () => {
        const input = document.getElementById('pdf-content');
        const pdf = new jsPDF('p', 'mm', 'a4');
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfHeight = pdf.internal.pageSize.getHeight();
        const marginBottom = 10;

        html2canvas(input, {
            scale: 2,
            useCORS: true,
            allowTaint: true,
            backgroundColor: '#ffffff'
        }).then((canvas) => {
            const imgData = canvas.toDataURL('image/png');
            const imgProps = pdf.getImageProperties(imgData);
            const imgHeight = (imgProps.height * pdfWidth) / imgProps.width;

            let heightLeft = imgHeight;
            let position = 0;

            pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, imgHeight);
            heightLeft -= pdfHeight;

            while (heightLeft >= 0) {
                position = heightLeft - imgHeight + marginBottom;
                pdf.addPage();
                pdf.addImage(imgData, 'PNG', 0, position, pdfWidth, imgHeight);
                heightLeft -= pdfHeight;
            }

            const fileName = `Inspection_Report_${data?.maskId || 'Report'}_${moment().format('DD-MM-YYYY')}.pdf`;
            pdf.save(fileName);
        });
    }

    const exportToExcel = () => {
        if (!data) return;

        const exportData = {
            'Report Information': {
                'Reference ID': data.maskId || '',
                'Inspection Type': data.checklist?.name || '',
                'Inspector': data.assignedTo?.firstName || '',
                'Scheduler': data.assignedBy?.firstName || '',
                'Location': [
                    data.locationOne?.name,
                    data.locationTwo?.name,
                    data.locationThree?.name,
                    data.locationFour?.name
                ].filter(Boolean).join(' - '),
                'Assigned Date': moment(data.created).format('DD/MM/YYYY'),
                'Due Date': moment(data.dateTime).format('DD/MM/YYYY'),
                'Status': getStatusFromActionRatio(data),
                'Generated On': moment().format('DD/MM/YYYY HH:mm')
            }
        };

        // Add checklist data
        if (data.checklistReport && data.checklistReport.length > 0) {
            const checklistData = [];
            data.checklistReport.forEach((item, index) => {
                if (item.type === 'checklist-group' && item.checked) {
                    const questions = item.questions || [];
                    questions.forEach((question, qIndex) => {
                        const selectedOptions = question.options?.filter(opt => opt.checked === 1) || [];
                        checklistData.push({
                            'Section': item.label || `Section ${index + 1}`,
                            'Question': question.label || '',
                            'Answer': selectedOptions.map(opt => opt.label).join(', '),
                            'Remarks': question.remarks || ''
                        });
                    });
                }
            });
            exportData['Checklist Data'] = checklistData;
        }

        // Add action items
        if (totalAction && totalAction.length > 0) {
            const actionData = totalAction.map((action, index) => ({
                'Action #': index + 1,
                'Description': action.data?.[0]?.description || '',
                'Status': action.lastStatus || '',
                'Action Type': action.lastActionType || '',
                'Assigned To': action.lastAssignedToId || ''
            }));
            exportData['Action Items'] = actionData;
        }

        const wb = XLSX.utils.book_new();

        Object.keys(exportData).forEach(sheetName => {
            const ws = XLSX.utils.json_to_sheet(exportData[sheetName]);
            XLSX.utils.book_append_sheet(wb, ws, sheetName);
        });

        const fileName = `Inspection_Report_${data?.maskId || 'Report'}_${moment().format('DD-MM-YYYY')}.xlsx`;
        XLSX.writeFile(wb, fileName);
    }

    const downloadInspectionReport = (id) => {
       

        // Call the download-inspection-report API endpoint
        window.open(`${API_URL}/download-inspection-pdf/${id}`, '_blank');
    }

    const header = () => {
        return (
            <div className='d-flex justify-content-end align-items-end'>
                {/* {hasPTWApplicantRole && <>
                                   <Button label="Apply Construction Permit" icon="pi pi-plus" className="p-button me-3" onClick={() => setConshow(true)} />
                                   <Button label="Apply DC Permit" icon="pi pi-plus" className="p-button me-3" onClick={() => setDcshow(true)} />
                               </>} */}



            </div>
        )
    }
    const reactToPrintFn = useReactToPrint({ contentRef });

    const dueDateBodyTemplate = (row) => {
        return (
            moment(row.dateTime, "DD/MM/YYYY").format("Do MMM YYYY")
        )
    }

    const convertToLocalTime = (gmtDate) => {
        // Get the system's time zone
        const systemTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

        // Define formats to handle
        const customFormat = 'DD-MM-YYYY'; // Format for "23-07-2024 13:35"

        let localDate;

        if (moment(gmtDate, customFormat, true).isValid()) {
            // If the input matches the custom format
            localDate = moment.tz(gmtDate, customFormat, 'GMT').tz(systemTimeZone).format('Do MMM YYYY');
        } else if (moment(gmtDate).isValid()) {
            // If the input is a valid ISO 8601 date
            localDate = moment.tz(gmtDate, 'GMT').tz(systemTimeZone).format('Do MMM YYYY');
        } else {
            throw new Error('Invalid date format');
        }

        return localDate;
    };
    return (
        <>

            <div>
                <div className="row">
                    <div className="col-12">
                        <div className="card">
                            <div className="">

                                <h4 className="card-title"></h4>
                                <div className="row">
                                    <div className="col-12">
                                        <div>



                                            <DataTable value={filterData} filters={filters} header={header} paginator rows={10} paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                                                rowsPerPageOptions={[10, 25, 50]}
                                                emptyMessage="No Data found.">
                                                <Column field='maskId' header='ID' body={maskIdBodyTemplate} ></Column>
                                                <Column field='checklist.name' header='Type of Inspection' filterElement={checklistFilterTemplate} filter showFilterMatchModes={false}></Column>
                                                <Column field="location" header="Location" body={locationBodyTemplate} />
                                                <Column field='dateTime' header="DueDate" body={dueDateBodyTemplate}></Column>
                                                <Column field="assignedTo.firstName" header="Assignee" filterElement={assigneeFilterTemplate} filter showFilterMatchModes={false} />
                                                <Column field="status" header="Status" body={(rowData) => getStatusFromActionRatio(rowData)} />
                                                <Column field='' header="Action Status" body={actionBodyTemplate}></Column>


                                            </DataTable>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <Modal
                show={showModal}
                size="lg"
                onHide={() => setShowModal(false)}
                aria-labelledby="example-modal-sizes-title-md"

            >


                <Modal.Body>
                    <Box>
                        {data && <div className="container" id='pdf-content' ref={contentRef}>
                            {/* Professional Header Section */}
                            <div className="report-header" style={{
                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                color: 'white',
                                padding: '2rem',
                                borderRadius: '12px',
                                marginBottom: '2rem',
                                position: 'relative',
                                overflow: 'hidden'
                            }}>
                                {/* Background Pattern */}
                                <div style={{
                                    position: 'absolute',
                                    top: 0,
                                    right: 0,
                                    width: '200px',
                                    height: '200px',
                                    background: 'rgba(255,255,255,0.1)',
                                    borderRadius: '50%',
                                    transform: 'translate(50px, -50px)'
                                }}></div>

                                <div className="row align-items-center">
                                    <div className="col-md-8">
                                        <div className="d-flex align-items-center mb-3">
                                            <i className="pi pi-file-check me-3" style={{ fontSize: '2.5rem' }}></i>
                                            <div>
                                                <h2 className="mb-1" style={{ fontWeight: '700', fontSize: '1.8rem' }}>
                                                    Inspection Report
                                                </h2>
                                                <h4 className="mb-0" style={{ fontWeight: '400', opacity: '0.9' }}>
                                                    {data.checklist?.name || 'Safety Inspection'}
                                                </h4>
                                            </div>
                                        </div>
                                        <div className="d-flex align-items-center">
                                            <span className="me-2" style={{ fontSize: '14px', opacity: '0.8' }}>Status:</span>
                                            <span className="badge" style={{
                                                backgroundColor: (() => {
                                                    const status = getStatusFromActionRatio(data);
                                                    if (status === "Completed with all actions closed") return 'rgba(40, 167, 69, 0.9)';
                                                    if (status === "Completed with Open Actions") return 'rgba(255, 193, 7, 0.9)';
                                                    if (status === "Completed") return 'rgba(40, 167, 69, 0.9)';
                                                    return 'rgba(108, 117, 125, 0.9)';
                                                })(),
                                                color: (() => {
                                                    const status = getStatusFromActionRatio(data);
                                                    if (status === "Completed with Open Actions") return '#000';
                                                    return '#fff';
                                                })(),
                                                padding: '8px 16px',
                                                borderRadius: '25px',
                                                fontSize: '13px',
                                                fontWeight: '600',
                                                border: '2px solid rgba(255,255,255,0.3)'
                                            }}>
                                                {getStatusFromActionRatio(data)}
                                            </span>
                                        </div>
                                    </div>
                                    <div className="col-md-4 text-end">
                                        <div className="export-actions">
                                            <Dropdown>
                                                <Dropdown.Toggle
                                                    variant="light"
                                                    className="d-flex align-items-center"
                                                    style={{
                                                        backgroundColor: 'rgba(255,255,255,0.95)',
                                                        border: 'none',
                                                        borderRadius: '8px',
                                                        padding: '12px 20px',
                                                        fontSize: '14px',
                                                        fontWeight: '600',
                                                        color: '#495057',
                                                        boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
                                                    }}
                                                >
                                                    <i className="pi pi-download me-2"></i>
                                                    Export Report
                                                </Dropdown.Toggle>

                                                <Dropdown.Menu style={{ borderRadius: '8px', border: 'none', boxShadow: '0 8px 25px rgba(0,0,0,0.15)' }}>
                                                    <Dropdown.Item
                                                        onClick={() => downloadInspectionReport(data.id)}
                                                        className="d-flex align-items-center"
                                                        style={{ padding: '12px 20px' }}
                                                    >
                                                        <i className="pi pi-file-pdf me-2" style={{ color: '#dc3545' }}></i>
                                                        Export as PDF
                                                    </Dropdown.Item>
                                                    <Dropdown.Item
                                                        onClick={exportToExcel}
                                                        className="d-flex align-items-center"
                                                        style={{ padding: '12px 20px' }}
                                                    >
                                                        <i className="pi pi-file-excel me-2" style={{ color: '#28a745' }}></i>
                                                        Export as Excel
                                                    </Dropdown.Item>
                                                    <Dropdown.Item
                                                        onClick={generatePdf}
                                                        className="d-flex align-items-center"
                                                        style={{ padding: '12px 20px' }}
                                                    >
                                                        <i className="pi pi-image me-2" style={{ color: '#007bff' }}></i>
                                                        High Quality PDF
                                                    </Dropdown.Item>
                                                    <Dropdown.Item
                                                        onClick={downloadInspectionReport}
                                                        className="d-flex align-items-center"
                                                        style={{ padding: '12px 20px' }}
                                                    >
                                                        <i className="pi pi-download me-2" style={{ color: '#6f42c1' }}></i>
                                                        Download Inspection Report
                                                    </Dropdown.Item>
                                                </Dropdown.Menu>
                                            </Dropdown>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Main Content Card */}
                            <div className="card" style={{
                                border: 'none',
                                borderRadius: '12px',
                                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                                background: '#ffffff'
                            }}>
                                <div className="card-body p-4">
                                    {/* Report Summary Section */}
                                    <div className="mb-4">
                                        <h5 style={{
                                            color: '#2c3e50',
                                            fontSize: '1.25rem',
                                            fontWeight: '700',
                                            marginBottom: '1.5rem',
                                            borderBottom: '3px solid #3498db',
                                            paddingBottom: '0.5rem',
                                            display: 'inline-block'
                                        }}>
                                            <i className="pi pi-info-circle me-2" style={{ color: '#3498db' }}></i>
                                            Report Summary
                                        </h5>
                                    </div>

                                    {/* Information Grid */}
                                    <div className="row g-4 mb-4">
                                        {/* Left Column */}
                                        <div className="col-md-6">
                                            {/* Reference # */}
                                            <div className="info-item mb-4" style={{
                                                background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                                                padding: '1rem',
                                                borderRadius: '8px',
                                                border: '1px solid #dee2e6'
                                            }}>
                                                <div className="d-flex align-items-center mb-2">
                                                    <i className="pi pi-hashtag me-2" style={{ color: '#6c757d', fontSize: '1rem' }}></i>
                                                    <span style={{ color: '#6c757d', fontSize: '13px', fontWeight: '600', textTransform: 'uppercase', letterSpacing: '0.5px' }}>Reference ID</span>
                                                </div>
                                                <div style={{ color: '#2c3e50', fontSize: '1.1rem', fontWeight: '700' }}>
                                                    {data.maskId || 'INS-250514-0081'}
                                                </div>
                                            </div>

                                            {/* Scheduler */}
                                            <div className="info-item mb-4" style={{
                                                background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                                                padding: '1rem',
                                                borderRadius: '8px',
                                                border: '1px solid #dee2e6'
                                            }}>
                                                <div className="d-flex align-items-center mb-2">
                                                    <i className="pi pi-user-plus me-2" style={{ color: '#6c757d', fontSize: '1rem' }}></i>
                                                    <span style={{ color: '#6c757d', fontSize: '13px', fontWeight: '600', textTransform: 'uppercase', letterSpacing: '0.5px' }}>Scheduler</span>
                                                </div>
                                                <div style={{ color: '#2c3e50', fontSize: '1.1rem', fontWeight: '700' }}>
                                                    {data.assignedBy?.firstName || 'John Smith'}
                                                </div>
                                            </div>

                                            {/* Location */}
                                            <div className="info-item mb-4" style={{
                                                background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                                                padding: '1rem',
                                                borderRadius: '8px',
                                                border: '1px solid #dee2e6'
                                            }}>
                                                <div className="d-flex align-items-center mb-2">
                                                    <i className="pi pi-map-marker me-2" style={{ color: '#6c757d', fontSize: '1rem' }}></i>
                                                    <span style={{ color: '#6c757d', fontSize: '13px', fontWeight: '600', textTransform: 'uppercase', letterSpacing: '0.5px' }}>Location</span>
                                                </div>
                                                <div style={{ color: '#2c3e50', fontSize: '1rem', fontWeight: '600', lineHeight: '1.4' }}>
                                                    {[
                                                        data.locationOne?.name,
                                                        data.locationTwo?.name,
                                                        data.locationThree?.name,
                                                        data.locationFour?.name
                                                    ]
                                                        .filter(Boolean)
                                                        .join(" → ") || 'India-IPN → Noida → Construction Projects → Project Noida DC 1'}
                                                </div>
                                            </div>
                                        </div>

                                        {/* Right Column */}
                                        <div className="col-md-6">
                                            {/* Inspector */}
                                            <div className="info-item mb-4" style={{
                                                background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                                                padding: '1rem',
                                                borderRadius: '8px',
                                                border: '1px solid #dee2e6'
                                            }}>
                                                <div className="d-flex align-items-center mb-2">
                                                    <i className="pi pi-user me-2" style={{ color: '#6c757d', fontSize: '1rem' }}></i>
                                                    <span style={{ color: '#6c757d', fontSize: '13px', fontWeight: '600', textTransform: 'uppercase', letterSpacing: '0.5px' }}>Inspector</span>
                                                </div>
                                                <div style={{ color: '#2c3e50', fontSize: '1.1rem', fontWeight: '700' }}>
                                                    {data.assignedTo?.firstName || 'Aditi Internal'}
                                                </div>
                                            </div>

                                            {/* Assigned Date */}
                                            <div className="info-item mb-4" style={{
                                                background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                                                padding: '1rem',
                                                borderRadius: '8px',
                                                border: '1px solid #dee2e6'
                                            }}>
                                                <div className="d-flex align-items-center mb-2">
                                                    <i className="pi pi-calendar-plus me-2" style={{ color: '#6c757d', fontSize: '1rem' }}></i>
                                                    <span style={{ color: '#6c757d', fontSize: '13px', fontWeight: '600', textTransform: 'uppercase', letterSpacing: '0.5px' }}>Assigned Date</span>
                                                </div>
                                                <div style={{ color: '#2c3e50', fontSize: '1.1rem', fontWeight: '700' }}>
                                                    {moment(data.created).format('Do MMM YYYY') || '12th May 2023'}
                                                </div>
                                            </div>

                                            {/* Due Date */}
                                            <div className="info-item mb-4" style={{
                                                background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                                                padding: '1rem',
                                                borderRadius: '8px',
                                                border: '1px solid #dee2e6'
                                            }}>
                                                <div className="d-flex align-items-center mb-2">
                                                    <i className="pi pi-calendar-times me-2" style={{ color: '#6c757d', fontSize: '1rem' }}></i>
                                                    <span style={{ color: '#6c757d', fontSize: '13px', fontWeight: '600', textTransform: 'uppercase', letterSpacing: '0.5px' }}>Due Date</span>
                                                </div>
                                                <div style={{ color: '#2c3e50', fontSize: '1.1rem', fontWeight: '700' }}>
                                                    {moment(data.dateTime).format('Do MMM YYYY') || '16th May 2023'}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Checklists Section */}
                                    <div className="mb-5">
                                        <h5 style={{
                                            color: '#2c3e50',
                                            fontSize: '1.25rem',
                                            fontWeight: '700',
                                            marginBottom: '1.5rem',
                                            borderBottom: '3px solid #e74c3c',
                                            paddingBottom: '0.5rem',
                                            display: 'inline-block'
                                        }}>
                                            <i className="pi pi-list-check me-2" style={{ color: '#e74c3c' }}></i>
                                            Inspection Checklists
                                        </h5>
                                        <div style={{
                                            background: 'linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%)',
                                            padding: '1.5rem',
                                            borderRadius: '12px',
                                            border: '1px solid #e9ecef'
                                        }}>
                                            <FormRender formData={data.checklistReport || []} />
                                        </div>
                                    </div>

                                    {/* Actions Section */}
                                    {totalAction && totalAction.length > 0 && (
                                        <div className="mb-4">
                                            <h5 style={{
                                                color: '#2c3e50',
                                                fontSize: '1.25rem',
                                                fontWeight: '700',
                                                marginBottom: '1.5rem',
                                                borderBottom: '3px solid #f39c12',
                                                paddingBottom: '0.5rem',
                                                display: 'inline-block'
                                            }}>
                                                <i className="pi pi-exclamation-triangle me-2" style={{ color: '#f39c12' }}></i>
                                                Action Items ({totalAction.length})
                                            </h5>
                                            <div style={{
                                                background: 'linear-gradient(135deg, #fefefe 0%, #f8f9fa 100%)',
                                                padding: '1.5rem',
                                                borderRadius: '12px',
                                                border: '1px solid #e9ecef'
                                            }}>
                                                <ActionTable id={maskId} actions={totalAction} current={current} />
                                            </div>
                                        </div>
                                    )}

                                    {/* Report Footer */}
                                    <div className="mt-5 pt-4" style={{
                                        borderTop: '2px solid #e9ecef',
                                        background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                                        padding: '1.5rem',
                                        borderRadius: '8px',
                                        textAlign: 'center'
                                    }}>
                                        <div className="row align-items-center">
                                            <div className="col-md-6 text-start">
                                                <small style={{ color: '#6c757d', fontSize: '12px' }}>
                                                    <i className="pi pi-calendar me-1"></i>
                                                    Generated on: {moment().format('Do MMMM YYYY, h:mm A')}
                                                </small>
                                            </div>
                                            <div className="col-md-6 text-end">
                                                <small style={{ color: '#6c757d', fontSize: '12px' }}>
                                                    <i className="pi pi-shield me-1"></i>
                                                    Safety & Inspection Management System
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>}
                    </Box>

                </Modal.Body>
                <Modal.Footer>
                    <Button
                        variant="light"
                        onClick={() => {

                            setShowModal(false);


                        }}
                    >
                        Close
                    </Button>
                </Modal.Footer>
            </Modal>
            {auditActionModal &&
                <Modal
                    show={auditActionModal}
                    size="lg"
                    onHide={() => setAuditActionModal(false)}
                    aria-labelledby="example-modal-sizes-title-md"
                >
                    <Modal.Body>

                        <ActionTable id={maskId} actions={totalAction} current={current} />
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            variant="light"
                            onClick={() => {
                                setAuditActionModal(false);
                            }}
                        >
                            Close
                        </Button>
                    </Modal.Footer>
                </Modal>
            }
        </>
    )
}

export default InspectionTable;
