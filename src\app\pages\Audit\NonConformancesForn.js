import React, { useEffect, useState } from "react";
import { DropzoneArea } from 'material-ui-dropzone';
import axios from "axios";
import API from "../../services/API";
import { AUDIT_FINDINGS_ASSIGN_ACTION_URL, AUDIT_GMS3_URL, AUDIT_WITH_ID_URL, GET_USERS_BY_ROLE, INSPECTION_ACTION_PLAN_REVIEWER, STATIC_URL } from "../../constants";

const NonConformancesForm = ({ gms, aformData, handleChange, handleFileChange, auditId }) => {

    // Form fields for Non-Conformances
    const [audit, setAudit] = useState(null);
    const [users, setUsers] = useState([])
    const [dueDate, setDueDate] = useState(new Date())
    const [selectedUser, setSelecedUser] = useState('')
    const [file, setFile] = useState([])

    

    useEffect(() => {
        if (audit) {
            getActionImplementors();
        }
    }, [audit])
    const fetchImageFile = async (url) => {
        const response = await fetch(url);
        const blob = await response.blob();
        const file = new File([blob], url.split('/').pop(), { type: blob.type });
        console.log(file)
        return file;
    };
    useEffect(() => {

        const addFiles = async () => {
            const urls = aformData.uploads ? aformData.uploads.map(i => {
                return `${STATIC_URL}/${i}`
            }) : []
            const fetchedFiles = await Promise.all(urls.map(url => fetchImageFile(url)));
            console.log(fetchedFiles)
            setFile(fetchedFiles)

        }
        addFiles();

    }, [])


    useEffect(() => {
        const getAuditInfo = async () => {

            const response = await API.get(AUDIT_WITH_ID_URL(auditId))
            if (response.status === 200) {
                setAudit(response.data)
            }
        }
        getAuditInfo();
    }, [])

    const getActionImplementors = async () => {
        const response = await API.post(GET_USERS_BY_ROLE, { locationOneId: audit.locationOneId, locationTwoId: audit.locationOneId, locationThreeId: audit.locationOneId, locationFourId: audit.locationOneId, mode: 'audit-action-plan-implementor' });
        if (response.status === 200) {
            setUsers(response.data)
        }
    }

   




    return (
        <form>
            <h2>Non-Conformances Form</h2>
            {/* Add your form fields here */}

            <div className="mb-3">
                <label htmlFor="inspectionCategories" className="form-label">
                    GMS Section
                </label>
                <select

                    className="form-control"
                    id="inspectionCategories"
                    name="inspectionCategories"
                    onChange={handleChange}
                    value={aformData.inspectionCategories.id}
                >
                    <option value="">Choose GMS Section</option>
                    {
                        gms.map(i => (<option key={i.id} value={i.id}>{i.name}</option>))
                    }
                </select>
            </div>
            <div className="mb-3">
                <label htmlFor="findings" className="form-label">
                    Findings
                </label>
                <textarea
                    className="form-control"
                    id="findings"
                    name="findings"
                    onChange={handleChange}
                    value={aformData.findings}

                ></textarea>
            </div>
            <div className="mb-3">
                <label htmlFor="evidencePhotos" className="form-label">
                    Upload Photos (Evidence)
                </label>
                {file.length != 0 &&
                    <DropzoneArea
                        initialFiles={file}
                        acceptedFiles={[
                            'image/jpeg',
                            'image/png'
                        ]}
                        dropzoneText={"Drag and Drop Evidence Images"}
                        filesLimit={5}
                        maxFileSize={104857600}
                        onChange={handleFileChange}
                        previewGridProps={{ container: { spacing: 2, direction: 'row', md: 12 } }}
                    />
                }
                {file.length === 0 &&
                    <DropzoneArea
                        acceptedFiles={[
                            'image/jpeg',
                            'image/png'
                        ]}
                        dropzoneText={"Drag and Drop Evidence Images"}
                        filesLimit={5}
                        maxFileSize={104857600}
                        onChange={handleFileChange}
                        previewGridProps={{ container: { spacing: 2, direction: 'row', md: 12 } }}
                    />
                }
            </div>

            <div className="mb-3">
                <label className="form-label">Classification</label>
                <div className="form-check">
                    <input
                        className="form-check-input"
                        type="radio"
                        id="minor"
                        name="classification"
                        value="minor"
                        onChange={handleChange}
                        checked={aformData.classification === "minor"}
                    />
                    <label className="form-check-label" htmlFor="minor">
                        Minor
                    </label>
                </div>
                {/* <div className="form-check">
                    <input
                        className="form-check-input"
                        type="radio"
                        id="medium"
                        name="classification"
                        value="medium"
                        onChange={handleChange}
                        checked={aformData.classification === "medium"}

                    />
                    <label className="form-check-label" htmlFor="medium">
                        Medium
                    </label>
                </div> */}
                <div className="form-check">
                    <input
                        className="form-check-input"
                        type="radio"
                        id="major"
                        name="classification"
                        value="major"
                        onChange={handleChange}
                        checked={aformData.classification === "major"}

                    />
                    <label className="form-check-label" htmlFor="major">
                        Major
                    </label>
                </div>
              
            </div>
            <div className="mb-3">
                <label htmlFor="standardsAndReferences" className="form-label">
                    Standards & References
                </label>
                <input
                    type="text"
                    className="form-control"
                    id="standardsAndReferences"
                    name="standardsAndReferences"
                    onChange={handleChange}
                    value={aformData.standardsAndReferences}

                />
            </div>
            <div className="mb-3">
                <label htmlFor="potentialHazard" className="form-label">
                    Potential Consequences
                </label>
                <input
                    type="text"
                    className="form-control"
                    id="potentialHazard"
                    name="potentialHazard"
                    onChange={handleChange}
                    value={aformData.potentialHazard}

                />
            </div>
            {/* Time Frame */}
            <div className="mb-3">
                <label htmlFor="recommendations" className="form-label">
                    Recommended Mitigation Measures
                </label>
                <textarea
                    className="form-control"
                    id="recommendations"
                    name="recommendations"
                    onChange={handleChange}
                    value={aformData.recommendations}
                ></textarea>
            </div>
            {/* <div className="mb-3">
                <label htmlFor="timeFrame" className="form-label">
                    Time Frame for closure
                </label>
                <input
                    type="date"
                    className="form-control"
                    id="timeFrame"
                    name="timeFrame"
                    onChange={handleChange}
                    value={aformData.timeFrame}

                />
            </div> */}
            <div className="form-group">
                <label>Target date for closure</label>
                <input type="date" value={aformData.dueDate} name="dueDate" onChange={handleChange} className="form-control" />
            </div>
            <div className="form-group">
                <select className="form-select" name="assignedToId" value={aformData.assignedToId} onChange={handleChange}>
                    <option value={""}>Assign User</option>
                    {
                        users.map(i => <option value={i.id} key={i.id}>{i.firstName}</option>)
                    }
                </select>
            </div>

            {/* Standards & References */}


            {/* Recommendations */}


            {/* Corrective Action Taken */}



        </form>
    );
};

export default NonConformancesForm;